<!DOCTYPE html>
<html>
<head>
    <title>Test Unified Sharing System</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="assets/js/sharing.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        button { padding: 8px 16px; margin: 5px; background: #007cba; color: white; border: none; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Test Unified Sharing System</h1>
    <p><strong>Note:</strong> This is a test page to verify the new unified sharing system. Delete this file after testing.</p>

    <div class="test-section">
        <h2>1. Test Document Sharing</h2>
        <p>Test sharing a document using the new generic system:</p>
        <input type="text" id="documentId" placeholder="Enter document UUID" style="width: 300px;">
        <button onclick="testDocumentShare()">Share Document</button>
        <div id="documentResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Deviation Sharing</h2>
        <p>Test sharing a deviation using the new generic system:</p>
        <input type="text" id="deviationId" placeholder="Enter deviation UUID" style="width: 300px;">
        <button onclick="testDeviationShare()">Share Deviation</button>
        <div id="deviationResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>3. Test Event Analysis Sharing</h2>
        <p>Test sharing an event analysis using the new generic system:</p>
        <input type="text" id="eventId" placeholder="Enter event analysis UUID" style="width: 300px;">
        <button onclick="testEventShare()">Share Event Analysis</button>
        <div id="eventResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>4. Test Risk Assessment Sharing</h2>
        <p>Test sharing a risk assessment using the new generic system:</p>
        <input type="text" id="riskId" placeholder="Enter risk assessment UUID" style="width: 300px;">
        <button onclick="testRiskShare()">Share Risk Assessment</button>
        <div id="riskResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>5. Test Public Share URL</h2>
        <p>Test accessing a public share URL:</p>
        <input type="text" id="shareId" placeholder="Enter share UUID" style="width: 300px;">
        <button onclick="testPublicShare()">Open Public Share</button>
        <div id="publicResult" class="result" style="display: none;"></div>
    </div>

    <script>
        // Set up global variables for testing
        window.csrfTokenName = 'csrf_token';
        window.csrfTokenValue = 'test_token'; // In real app, this would be dynamic
        
        // Override showNotification for testing
        window.showNotification = function(message, type) {
            console.log(type.toUpperCase() + ': ' + message);
        };

        function testDocumentShare() {
            var documentId = document.getElementById('documentId').value;
            if (!documentId) {
                showResult('documentResult', 'Please enter a document UUID', 'error');
                return;
            }
            
            showResult('documentResult', 'Testing document sharing...', 'info');
            
            $.ajax({
                url: '/shares/track',
                type: 'POST',
                data: {
                    entity_type: 'document',
                    entity_id: documentId,
                    csrf_token: 'test_token'
                },
                dataType: 'json',
                success: function(response) {
                    showResult('documentResult', 'Success! Share ID: ' + response.share_id + '<br>Public URL: <a href="' + response.public_url + '" target="_blank">' + response.public_url + '</a>', 'success');
                },
                error: function(xhr, status, error) {
                    showResult('documentResult', 'Error: ' + xhr.responseText, 'error');
                }
            });
        }

        function testDeviationShare() {
            var deviationId = document.getElementById('deviationId').value;
            if (!deviationId) {
                showResult('deviationResult', 'Please enter a deviation UUID', 'error');
                return;
            }
            
            trackEntityShare('deviation', deviationId, 'csrf_token', 'test_token');
            showResult('deviationResult', 'Deviation sharing test initiated - check console for results', 'info');
        }

        function testEventShare() {
            var eventId = document.getElementById('eventId').value;
            if (!eventId) {
                showResult('eventResult', 'Please enter an event analysis UUID', 'error');
                return;
            }
            
            trackEntityShare('event', eventId, 'csrf_token', 'test_token');
            showResult('eventResult', 'Event analysis sharing test initiated - check console for results', 'info');
        }

        function testRiskShare() {
            var riskId = document.getElementById('riskId').value;
            if (!riskId) {
                showResult('riskResult', 'Please enter a risk assessment UUID', 'error');
                return;
            }
            
            trackEntityShare('risk', riskId, 'csrf_token', 'test_token');
            showResult('riskResult', 'Risk assessment sharing test initiated - check console for results', 'info');
        }

        function testPublicShare() {
            var shareId = document.getElementById('shareId').value;
            if (!shareId) {
                showResult('publicResult', 'Please enter a share UUID', 'error');
                return;
            }
            
            var publicUrl = '/share/view/' + shareId;
            window.open(publicUrl, '_blank');
            showResult('publicResult', 'Opened public share URL: <a href="' + publicUrl + '" target="_blank">' + publicUrl + '</a>', 'success');
        }

        function showResult(elementId, message, type) {
            var element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = 'result ' + type;
            element.style.display = 'block';
        }
    </script>
</body>
</html>
