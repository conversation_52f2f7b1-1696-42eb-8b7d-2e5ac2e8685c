<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<h1>
				<?php echo lang('shareable_documents'); ?>
				<small><?php echo lang('shareable_documents_desc'); ?></small>
			</h1>
		</section>

	<!-- Main content -->
	<section class="content">
		<!-- Info boxes -->
		<div class="row">
			<div class="col-md-3 col-sm-6 col-xs-12">
				<div class="info-box">
					<span class="info-box-icon bg-aqua"><i class="fa fa-share-alt"></i></span>
					<div class="info-box-content">
						<span class="info-box-text"><?php echo lang('shareable_total_shared'); ?></span>
						<span class="info-box-number"><?php echo $stats['total_shared']; ?></span>
					</div>
				</div>
			</div>
			<div class="col-md-3 col-sm-6 col-xs-12">
				<div class="info-box">
					<span class="info-box-icon bg-green"><i class="fa fa-calendar"></i></span>
					<div class="info-box-content">
						<span class="info-box-text"><?php echo lang('shareable_this_month'); ?></span>
						<span class="info-box-number"><?php echo $stats['this_month']; ?></span>
					</div>
				</div>
			</div>
			<div class="col-md-3 col-sm-6 col-xs-12">
				<div class="info-box">
					<span class="info-box-icon bg-yellow"><i class="fa fa-clock-o"></i></span>
					<div class="info-box-content">
						<span class="info-box-text"><?php echo lang('shareable_this_week'); ?></span>
						<span class="info-box-number"><?php echo $stats['this_week']; ?></span>
					</div>
				</div>
			</div>
			<div class="col-md-3 col-sm-6 col-xs-12">
				<div class="info-box">
					<span class="info-box-icon bg-red"><i class="fa fa-files-o"></i></span>
					<div class="info-box-content">
						<span class="info-box-text"><?php echo lang('shareable_total_results'); ?></span>
						<span class="info-box-number"><?php echo count($shared_documents); ?></span>
					</div>
				</div>
			</div>
		</div>

		<!-- Documents list -->
		<div class="row">
			<div class="col-xs-12">
				<div class="box">
					<div class="box-header with-border">
						<h3 class="box-title">
							<?php echo lang('shareable_documents_list'); ?>
							<small>(<?php echo count($shared_documents); ?> <?php echo lang('shareable_documents_count'); ?>)</small>
						</h3>
					</div>
					<div class="box-body">
						<?php if (!empty($shared_documents)): ?>
							<div class="table-responsive">
								<table class="table table-bordered table-striped">
									<thead>
										<tr>
											<th><?php echo lang('shareable_document_name'); ?></th>
											<th><?php echo lang('shareable_folder'); ?></th>
											<th><?php echo lang('shareable_menu'); ?></th>
											<th style="width: 200px;"><?php echo lang('shareable_shared_by'); ?></th>
											<th><?php echo lang('shareable_share_date'); ?></th>
											<th><?php echo lang('shareable_actions'); ?></th>
										</tr>
									</thead>
									<tbody>
										<?php foreach ($shared_documents as $doc): ?>
											<tr>
												<td>
													<div class="document-info">
														<?php 
														$file_type = '';
														if (!empty($doc->file_ext)) {
															$file_type = $doc->file_ext == '.docx' ? '<img src="/resources/css/images/file_docx.svg" style="width: 16px; height: 16px; margin-right: 5px; vertical-align: middle;"/>' :
															($doc->file_ext == '.xlsx' ? '<img src="/resources/css/images/file_xlsx.svg" style="width: 16px; height: 16px; margin-right: 5px; vertical-align: middle;"/>' :
															($doc->file_ext == '.pptx' ? '<img src="/resources/css/images/file_pptx.svg" style="width: 16px; height: 16px; margin-right: 5px; vertical-align: middle;"/>' : ''));
														}
														?>
														<strong><?php echo $file_type; ?><a href="<?php echo site_url('documentview/view/' . $doc->document_id); ?>" target="_blank" title="<?php echo lang('shareable_view_document'); ?>"><?php echo htmlspecialchars($doc->document_name); ?></a></strong>
														<?php if (!empty($doc->document_description)): ?>
															<br><small class="text-muted"><?php echo htmlspecialchars(substr($doc->document_description, 0, 100)); ?><?php echo strlen($doc->document_description) > 100 ? '...' : ''; ?></small>
														<?php endif; ?>
													</div>
												</td>
												<td><?php echo htmlspecialchars($doc->folder_name); ?></td>
												<td><?php echo htmlspecialchars($doc->menu_name); ?></td>
												<td><?php echo htmlspecialchars($doc->shared_by_name); ?></td>
												<td>
													<span title="<?php echo sprintf(lang('shareable_first_shared'), date('Y-m-d H:i:s', strtotime($doc->shared_date))); ?>">
														<?php echo date('Y-m-d H:i', strtotime($doc->shared_date)); ?>
													</span>
												</td>
												<td>
													<div class="btn-group">
														<button type="button" class="btn btn-sm btn-info"
																onclick="copyShareLink('<?php echo htmlspecialchars($doc->public_url); ?>')"
																title="<?php echo lang('shareable_copy_link'); ?>">
															<i class="fa fa-copy"></i>
														</button>
														<button type="button" class="btn btn-sm btn-danger" 
																onclick="removeShare('<?php echo htmlspecialchars($doc->document_id); ?>')"
																title="<?php echo lang('shareable_remove_share'); ?>">
															<i class="fa fa-times"></i>
														</button>
													</div>
												</td>
											</tr>
										<?php endforeach; ?>
									</tbody>
								</table>
							</div>

						<?php else: ?>
							<div class="alert alert-info text-center">
								<i class="fa fa-info-circle"></i>
								<?php echo lang('shareable_no_documents'); ?>
							</div>
						<?php endif; ?>
					</div>
				</div>
			</div>
		</div>
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->

<script>
function copyShareLink(url) {
	if (navigator.clipboard) {
		navigator.clipboard.writeText(url).then(function() {
			showNotification('<?php echo lang('shareable_link_copied'); ?>', 'success');
		}).catch(function() {
			fallbackCopyTextToClipboard(url);
		});
	} else {
		fallbackCopyTextToClipboard(url);
	}
}

function fallbackCopyTextToClipboard(text) {
	var textArea = document.createElement("textarea");
	textArea.value = text;
	document.body.appendChild(textArea);
	textArea.focus();
	textArea.select();
	try {
		var successful = document.execCommand('copy');
		if (successful) {
			showNotification('<?php echo lang('shareable_link_copied'); ?>', 'success');
		} else {
			showNotification('<?php echo lang('shareable_copy_failed'); ?>', 'error');
		}
	} catch (err) {
		showNotification('<?php echo lang('shareable_copy_failed'); ?>', 'error');
	}
	document.body.removeChild(textArea);
}

function removeShare(documentId) {
	if (!confirm('<?php echo lang('shareable_confirm_remove'); ?>')) {
		return;
	}

	var button = $('button[onclick="removeShare(\'' + documentId + '\')"]');

	$.ajax({
		url: '<?php echo site_url('admin/documents/remove_share'); ?>',
		type: 'POST',
		data: {
			document_id: documentId,
			'<?php echo $this->security->get_csrf_token_name(); ?>': '<?php echo $this->security->get_csrf_hash(); ?>'
		},
		dataType: 'json',
		beforeSend: function() {
			// Disable the button and show loading state
			button.prop('disabled', true);
			button.html('<i class="fa fa-spinner fa-spin"></i>');
		},
		success: function(response) {
			if (response.success) {
				showNotification(response.message, 'success');
				// Remove the row from the table with animation
				button.closest('tr').fadeOut(500, function() {
					$(this).remove();
					// Update the document count
					updateDocumentCount();
				});
			} else {
				showNotification(response.message || '<?php echo lang('shareable_remove_failed'); ?>', 'error');
				// Reset button state
				button.prop('disabled', false);
				button.html('<i class="fa fa-times"></i>');
			}
		},
		error: function(xhr, status, error) {
			console.log('Failed to remove share:', error);
			console.log('Response:', xhr.responseText);
			showNotification('<?php echo lang('shareable_remove_failed'); ?>', 'error');
			// Reset button state
			button.prop('disabled', false);
			button.html('<i class="fa fa-times"></i>');
		}
	});
}

function updateDocumentCount() {
	var currentCount = $('.box-title small').text().match(/\d+/);
	if (currentCount) {
		var newCount = parseInt(currentCount[0]) - 1;
		$('.box-title small').text('(' + newCount + ' <?php echo lang('shareable_documents_count'); ?>)');
		
		// Update the info box for total results
		$('.info-box .bg-red + .info-box-content .info-box-number').text(newCount);
		
		// If no documents left, show the no documents message
		if (newCount === 0) {
			$('.table-responsive').html('<div class="alert alert-info text-center"><i class="fa fa-info-circle"></i> <?php echo lang('shareable_no_documents'); ?></div>');
		}
	}
}

function showNotification(message, type) {
	var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
	var notification = '<div class="alert ' + alertClass + ' alert-dismissible" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">' +
		'<button type="button" class="close" data-dismiss="alert">&times;</button>' +
		message +
		'</div>';
	$('body').append(notification);
	setTimeout(function() {
		$('.alert').fadeOut();
	}, 3000);
}
</script>

<?php $this->load->view('template/footer'); ?>
