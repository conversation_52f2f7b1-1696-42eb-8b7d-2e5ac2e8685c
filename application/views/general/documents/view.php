<script>
	window.addEventListener("load", function (){
		const urlParams = new URLSearchParams(window.location.search);
		const review = urlParams.get('review');
		<?php $fileuri = serverPath() . "/documentcallback/download_path/" . $auth_company_id . "/" . $main_attachment; ?>
		if (review == 'true') {
			setTimeout(() => {
				$('#iframe-div').html(
					`<iframe src="/doceditor/<?php echo $main_attachment ?>/<?php echo $document->document_id ?>?action=embedded&type=embedded&review=true" frameborder="0" style="width: 100%; height: calc(100vh - 200px);"></iframe>`
				);
			}, 9000);
		}
		else {
			$('#iframe-div').html(
				`<iframe src="/doceditor/<?php echo $main_attachment ?>/<?php echo $document->document_id ?>?action=embedded&type=embedded&review=true" frameborder="0" style="width: 100%; height: calc(100vh - 200px);"></iframe>`
			);	
		}

		$(".info-toggle").click(function(e) {
				e.preventDefault();
				if ($(".info-toggle").hasClass("active")) {
					$("#document-wrapper").toggleClass("toggled");
				} else {
					$("#document-wrapper").addClass("toggled");
				}
				$("#document-info-buttons .active:not(.info-toggle)").removeClass("active")
				$(".info-toggle").toggleClass("active");
				$("#document-info-box > div:not(#overview)").hide();
				$("#overview").toggle();
		});

	}, false)

	function closeInfoBox() {
		$("#document-wrapper").removeClass("toggled");
		$("#document-info-buttons .active").removeClass("active")
		$("#document-info-box > div").hide()
		$(".info-toggle").removeClass("active")
	}
	
</script>
<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group" style="display: flex;">
				<?php
				if ($document->status == 'draft') {
					echo form_button(array(
						'title' => lang('documents_draft_archive'),
						'class' => 'btn btn-primary dialog-confirm',
						'data-id' => $document->document_id,
						'data-other' => 'redirect',
						'data-redirect' => isset($_SERVER['HTTP_REFERER']) ? parse_url($_SERVER['HTTP_REFERER'], PHP_URL_PATH): null,
						'data-dialog' => '#confirm-delete-draft-dialog',
						'data-url' => base_url('documents/delete_draft'),
						'style' => "margin-right: 10px; height: 34px",
						'content' => '<i class="fa fa-trash" aria-hidden="true"></i> '
							)
						);
					}
				if(
					isset($menu['current']->owner) && (
						(
							$this->config->item('document_author') === TRUE &&
							(
								is_role('Systemadministratör')
								OR in_array($this->auth_user_id, [$document->owner, $menu['current']->owner])
								OR acl_object_permits('menu.author',[$menu['current']->menu_id])
								OR in_array($this->auth_user_id, $document_editors) OR
								acl_group_permits('menu.update',$groups) OR
								acl_object_permits('menu.update',[$menu['current']->menu_id])
							)
						)
						OR
						(
							$this->config->item('document_author') === FALSE &&
							(
								is_role('Systemadministratör') OR
								in_array($this->auth_user_id, [$document->owner, $menu['current']->owner]) OR
								in_array($this->auth_user_id, $document_editors) OR
								acl_group_permits('menu.update',$groups) OR
								acl_object_permits('menu.update',[$menu['current']->menu_id])
							)
						)
					)
				)
				{
					echo icon_anchor('documents/update', $document->parent_id ? $document->parent_id : $document->document_id, 
						lang('edit'),
						array(
						'title' => lang('edit'),
						'class' => 'btn btn-primary',
						'style' => 'margin-right: 8px'
						)
					);
				}
				?>
				<a class="btn btn-default" title="<?php echo lang('deviation_share') ?>" data-toggle="modal" data-target="#myShareModal" onclick="trackDocumentShare('<?php echo $document->document_id; ?>')">
					<i class="fa fa-share-alt" aria-hidden="true" style="margin-right: 3px; margin-top: 3px;"></i>
				</a>
				<a class="btn btn-default info-toggle mobile-only" style="margin-left: 8px"><svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M13 0L18 5V19.008C17.9997 19.2712 17.895 19.5235 17.7088 19.7095C17.5226 19.8955 17.2702 20 17.007 20H0.993C0.730378 19.9982 0.479017 19.8931 0.293218 19.7075C0.107418 19.5219 0.00209465 19.2706 0 19.008V0.992C0 0.444 0.445 0 0.993 0H13ZM8 5V7H10V5H8ZM8 9V15H10V9H8Z"
					/></svg>
				</a>
				<?php

				if( is_role('Systemadministratör') OR in_array($this->auth_user_id, [$document->owner, $menu['current']->owner]) OR ($this->auth_flex && $document->owner === $this->auth_user_id) )
				{
					echo '
					<div class="dropdown">
						<div href="#" class="btn btn-secodary dropdown-toggle" data-toggle="dropdown">
							<i class="fa fa-ellipsis-h" aria-hidden="true" style="margin-right: 5px"></i>
						</div>
						<ul class="dropdown-menu app-box new-doc-menu">';
					
						if( is_role('Systemadministratör') OR ($this->auth_flex && $document->owner === $this->auth_user_id) )
						{
							echo '<li>' . icon_anchor('documents/move', $document->document_id, lang('documents_move'),
								array(
								'title' => lang('documents_move')
								)
							) . '</li>';
						}
					echo '<li>' . form_button(array(
							'title' => lang('documents_archive'),
							'class' => 'dialog-confirm',
							'data-id' => $document->document_id,
							// 'data-title' => $attachment->file_name,
							'data-dialog' => '#confirm-dialog',
							'data-url' => base_url('documents/archive'),
							'content' => lang('documents_archive')
						)
					). '</li>';
					echo '
						</ul>
						</div>
					';
				}

				?>
				<a href="javascript:goBack('/perspective/<?php echo $folder->menu_id ?>/<?php echo $folder->folder_id ?>');" title="<?php echo lang('back') ?>" class="btn btn-secondary"
					style="color: inherit; height: 34px"><i class="fa fa-reply" aria-hidden="true" style="line-height: 20px;"></i></a>
			</div>
			<h1>
				<?php // @STEP2: Better path ?>
				<small><?php echo $menu['current']->name; ?> > <?php echo $folder->name; ?></small>
			</h1>
			<h1 style="padding: 20px 0 0 20px;">
				<?php echo $document->name; ?>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<?php if( $document->status != 'published' ): ?>
				<div class="alert alert-warning no-print"><?php echo sprintf(lang('documents_not_published'), username($menu['current']->owner)); ?></div>
			<?php endif; ?>
			<div id="document-wrapper" style="display: flex">
				<div id="document-box" style="width: 100%">
					<?php if( !empty($attachments) && !empty($document->content) && strlen($document->content_clean) >= 200 ): ?>
						<div class="box box-solid collapsed-box">
							<div class="box-header with-border">
								<h3 class="box-title"><?php echo lang('documents_attachments'); ?></h3>
								<div class="box-tools float-right">
									<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-plus"></i>
									</button>
								</div>
								<!-- /.box-tools -->
							</div>
							<div class="box-body">
								<table id="attachments-table" class="table table-striped table-bordered no-data-table">
									<colspan>
										<col>
										<col width="200px">
									</colspan>
									<thead>
										<tr>
											<th>Filnamn</th>
											<th>Uppladdad</th>
										</tr>
									</thead>
									<tbody>
										<?php foreach($attachments as $attachment): ?>
											<tr>
												<td><?php echo safe_anchor('documents/download', $attachment->attachment_id, $attachment->file_name, ['target' => '_blank']); ?></td>
												<td><?php echo $attachment->uploaded_on; ?></td>
											</tr>
										<?php endforeach; ?>
									</tbody>
								</table>
							</div>
						</div>
					<?php endif; ?>
					<div class="box box-solid">
						<div class="box-body">
							<p class="text-center">
								<?php echo nl2br($document->description); ?>
							</p>
							<?php if ($main_attachment): ?>
								<div id="iframe-div">
									<div class="loader"></div>
								</div>
							<?php elseif( !empty($document->content) ): ?>
								<iframe id="iframe-resize" src="<?php echo site_url('documents/inline/' . $document->document_id); ?>" frameborder="0" style="width: 100%; height: 100%;" tabindex="0" allowtransparency="true"></iframe>
							<?php endif; ?>
						</div>
					</div>
					<?php if( !empty($attachments) ): ?>
						<div class="box box-solid">
							<div class="box-header with-border">
								<h3 class="box-title"><?php echo lang('documents_attachments'); ?></h3>
							</div>
							<div class="box-body">
								<table id="attachments-table" class="table table-striped table-bordered no-data-table">
									<colspan>
										<col>
										<col width="200px">
										<col>
									</colspan>
									<thead>
										<tr>
											<th>Filnamn</th>
											<th>Uppladdad</th>
											<th></th>
										</tr>
									</thead>
									<tbody>
										<?php foreach($attachments as $attachment): ?>
											<tr>
												<td><?php echo safe_anchor('documents/update/' . $document->document_id . '?attachment=' . $attachment->attachment_id, '', $attachment->file_name); ?></td>
												<td><?php echo $attachment->uploaded_on; ?></td>
												<?php if (CI_ONLY_OFFICE): ?>
													<td><?php echo icon_anchor('documents/download', $attachment->attachment_id, '<i class="fa fa-download" aria-hidden="true"></i>',
														array(
														'target' => '_blank',
														'title' => lang('download'),
														'class' => 'btn btn-primary'
														)
													); ?></td>
												<?php endif; ?>
											</tr>
										<?php endforeach; ?>
									</tbody>
								</table>
							</div>
						</div>
					<?php endif; ?>
				</div>
				<div id="document-info-box">
					<div id="overview" style="display: none">
						<div class="document-info-box-title"><?php echo lang('documents_document_head'); ?>
							<div style="float: right" onClick="closeInfoBox()"><i class="fa fa-times" aria-hidden="true"></i></div>
						</div>
						<div class="box-body">
							<dl class="description-list">
								<dt><?php echo lang('documents_path'); ?></dt>
								<dd><?php echo $menu['current']->name; ?> > <?php echo $folder->name; ?></dd>
								<dt><?php echo lang('documents_created_by'); ?></dt>
								<dd><?php echo username($document->created_by); ?>, <?php echo substr($document->created_date, 0, 10); ?></dd>
								<dt><?php echo lang('documents_version'); ?></dt>
								<dd><?php echo $document_version; ?></dd>
								<dt><?php echo lang('documents_owner'); ?></dt>
								<dd><?php echo username($document->owner); ?></dd>
								<dt><?php echo lang('documents_type'); ?></dt>
								<dd><?php echo $documents_type[$document->document_type]; ?></dd>
								<dt><?php echo lang('documents_category'); ?></dt>
								<dd><?php echo isset($documents_category[$document->document_category]) ? $documents_category[$document->document_category] : ''; ?></dd>
								<dt><?php echo lang('documents_created'); ?></dt>
								<dd><?php echo $document->created; ?></dd>
								<dt><?php echo lang('documents_last_revised'); ?></dt>
								<dd><?php echo $document->last_revised; ?></dd>
								<?php if($document->valid_until !== '0000-00-00'): ?>
								<dt><?php echo lang('documents_valid_until'); ?></dt>
								<dd><?php echo $document->valid_until; ?></dd>
								<?php endif; ?>
								<dt><?php echo lang('documents_accepted_by'); ?></dt>
								<dd><?php echo username($menu['current']->owner); ?></dd>
								<?php if( $document->edited_date !== '0000-00-00 00:00:00' ): ?>
									<dt><?php echo lang('documents_edited_by'); ?></dt>
									<dd><?php echo username($document->edited_by); ?>, <?php echo $document->edited_date; ?></dd>
								<?php endif; ?>
								<?php if( !empty($document->edited_reason) ): ?>
									<dt><?php echo lang('documents_significant_changes'); ?></dt>
									<dd><?php echo $document->edited_reason; ?></dd>
								<?php endif; ?>
							</dl>
						</div>
					</div>
				</div>
				<div id="document-info-buttons">
					<div class="info-icon info-toggle"><svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M13 0L18 5V19.008C17.9997 19.2712 17.895 19.5235 17.7088 19.7095C17.5226 19.8955 17.2702 20 17.007 20H0.993C0.730378 19.9982 0.479017 19.8931 0.293218 19.7075C0.107418 19.5219 0.00209465 19.2706 0 19.008V0.992C0 0.444 0.445 0 0.993 0H13ZM8 5V7H10V5H8ZM8 9V15H10V9H8Z"
						/></svg>
					</div>
					<?php if (!CI_ONLY_OFFICE || !isset($main_attachment)): ?>
						<div class="info-icon">
							<i class="fa fa-print" aria-hidden="true" title="Skriv ut" style='color: #42526E; font-size: 20px;'
								onclick="document.getElementById('iframe-resize').contentWindow.print()"></i>
						</div>
					<?php endif; ?>
					<?php 
					if(
						isset($menu['current']->owner) && (
							is_role('Systemadministratör') OR
							in_array($this->auth_user_id, [$document->owner, $menu['current']->owner])
						)
					)
					{
						echo '<div class="info-icon">' .
						icon_anchor('documents/history', $document->document_id, '<i class="fa fa-history" aria-hidden="true"></i>',
							array(
							'title' => lang('documents_history'),
							'style' => 'color: #42526E; font-size: 20px;'
							)
						) . '</div>';
					}
					if( is_role('Systemadministratör') OR in_array($this->auth_user_id, [$document->owner, $menu['current']->owner]) OR ($this->auth_flex && $document->owner === $this->auth_user_id) )
					{
						echo '<div class="info-icon">' .
						icon_anchor('documents/readlog', $document->document_id, '<img src="/resources/img/icons/read-log.svg">',
							array(
							'title' => lang('documents_read_log'),
							)
						) . '</div>';
					}
					?>
				</div>
			</div>
			<!-- /.row -->
		</section>
		<!-- /.content -->
		<div id="confirm-dialog" style="display:none" title="<?php echo $document->name; ?>">
			<?php echo nl2br(sprintf(lang('documents_archive_help'),$this->auth_name)); ?>
		</div>
		<div id="confirm-delete-draft-dialog" style="display:none" title="<?php echo lang('documents_draft_archive'); ?>">
				<?php echo nl2br(sprintf(lang('documents_draft_archive_help'),$this->auth_name)); ?>
			</div>
		<div class="modal fade" id="myShareModal" tabindex="-1" role="dialog" aria-labelledby="myShareModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title" id="myShareModalLabel"><?php echo lang('deviation_share_link'); ?></h4>
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
					</div>
					<div class="modal-body">
						<a id="copy-text" href="https://<?php echo $_SERVER['SERVER_NAME'] ?>/documentview/view/<?php echo $document->document_id; ?>" target="_blank">https://<?php echo $_SERVER['SERVER_NAME'] ?>/documentview/view/<?php echo $document->document_id; ?></a>
						<button onClick="navigator.clipboard.writeText(document.getElementById('copy-text').innerText)"
						class="btn btn-default" style="float: right">
							<i class="fa fa-copy" aria-hidden="true"></i>
						</button>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- /.content-wrapper -->

	<script>
	function trackDocumentShare(documentId) {
		var shareUrl = 'https://<?php echo $_SERVER['SERVER_NAME'] ?>/documentview/view/' + documentId;

		$.ajax({
			url: '<?php echo site_url('documents/track_share'); ?>',
			type: 'POST',
			data: {
				document_id: documentId,
				share_url: shareUrl,
				'<?php echo $this->security->get_csrf_token_name(); ?>': '<?php echo $this->security->get_csrf_hash(); ?>'
			},
			dataType: 'json',
			success: function(response) {
				console.log('Share tracked successfully:', response);
			},
			error: function(xhr, status, error) {
				console.log('Failed to track share:', error);
				console.log('Response:', xhr.responseText);
			}
		});
	}
	</script>

<?php $this->load->view('template/footer-document'); ?>