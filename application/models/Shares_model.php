<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Shares_model extends Auth_Model
{
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Create a share for an entity
	 * If a share already exists, return the existing share_id without updating shared_date
	 * 
	 * @param string $entity_type The type of entity (document, deviation, event, risk)
	 * @param string $entity_id The UUID of the entity
	 * @return string|FALSE The share_id (UUID string) on success, FALSE on failure
	 */
	public function create_share($entity_type, $entity_id)
	{
		// Check if share already exists
		$existing_share = $this->get_by_entity($entity_type, $entity_id);
		if ($existing_share) {
			log_message('debug', 'Share already exists for ' . $entity_type . ': ' . $entity_id . ', returning existing share_id: ' . $existing_share->share_id);
			return $existing_share->share_id;
		}

		$share_id = UUIDv4();
		$data = array(
			'share_id'    => UUID_TO_BIN($share_id),
			'entity_type' => $entity_type,
			'entity_id'   => UUID_TO_BIN($entity_id),
			'shared_by'   => UUID_TO_BIN($this->auth_user_id),
			'shared_date' => date('Y-m-d H:i:s')
		);

		log_message('debug', 'Creating new share for ' . $entity_type . ': ' . $entity_id . ' by user: ' . $this->auth_user_id);

		if ($this->db->insert($this->db_table('shares'), $data)) {
			log_message('debug', 'Share created successfully with ID: ' . $share_id);
			return $share_id;
		}

		log_message('error', 'Failed to create share: ' . $this->db->last_query());
		log_message('error', 'Database error: ' . json_encode($this->db->error()));
		return FALSE;
	}

	/**
	 * Get share by share_id
	 * 
	 * @param string $share_id The UUID of the share
	 * @return object|NULL The share record or NULL if not found
	 */
	public function get_by_share_id($share_id)
	{
		$this->db->where('share_id', UUID_TO_BIN($share_id));
		$query = $this->db->get($this->db_table('shares'));

		if ($query->num_rows() > 0) {
			$result = $query->row();
			$result->share_id = BIN_TO_UUID($result->share_id);
			$result->entity_id = BIN_TO_UUID($result->entity_id);
			$result->shared_by = BIN_TO_UUID($result->shared_by);
			return $result;
		}

		return NULL;
	}

	/**
	 * Get share by entity type and ID
	 * 
	 * @param string $entity_type The type of entity
	 * @param string $entity_id The UUID of the entity
	 * @return object|NULL The share record or NULL if not found
	 */
	public function get_by_entity($entity_type, $entity_id)
	{
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		$query = $this->db->get($this->db_table('shares'));

		if ($query->num_rows() > 0) {
			$result = $query->row();
			$result->share_id = BIN_TO_UUID($result->share_id);
			$result->entity_id = BIN_TO_UUID($result->entity_id);
			$result->shared_by = BIN_TO_UUID($result->shared_by);
			return $result;
		}

		return NULL;
	}

	/**
	 * Delete share by entity type and ID
	 * 
	 * @param string $entity_type The type of entity
	 * @param string $entity_id The UUID of the entity
	 * @return bool TRUE on success, FALSE on failure
	 */
	public function delete_by_entity($entity_type, $entity_id)
	{
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		
		$result = $this->db->delete($this->db_table('shares'));
		
		if ($result) {
			log_message('debug', 'Share deleted for ' . $entity_type . ': ' . $entity_id);
		} else {
			log_message('error', 'Failed to delete share for ' . $entity_type . ': ' . $entity_id);
		}
		
		return $result;
	}

	/**
	 * Delete share by share_id
	 * 
	 * @param string $share_id The UUID of the share
	 * @return bool TRUE on success, FALSE on failure
	 */
	public function delete_by_share_id($share_id)
	{
		$this->db->where('share_id', UUID_TO_BIN($share_id));
		
		$result = $this->db->delete($this->db_table('shares'));
		
		if ($result) {
			log_message('debug', 'Share deleted by share_id: ' . $share_id);
		} else {
			log_message('error', 'Failed to delete share by share_id: ' . $share_id);
		}
		
		return $result;
	}

	/**
	 * Get all shares with entity details (for admin interface)
	 * 
	 * @param int $limit Limit number of results
	 * @param int $offset Offset for pagination
	 * @param string $search Search term
	 * @param string $entity_type Filter by entity type (optional)
	 * @return array Array of share records with entity details
	 */
	public function get_all_shares($limit = NULL, $offset = NULL, $search = NULL, $entity_type = NULL)
	{
		$this->db->select('shares.share_id, shares.entity_type, shares.entity_id, shares.shared_by, shares.shared_date, users.name as shared_by_name');
		$this->db->from($this->db_table('shares'));
		$this->db->join($this->db_table('user_table') . ' AS users', 'users.user_id = shares.shared_by', 'left');
		
		if ($entity_type) {
			$this->db->where('shares.entity_type', $entity_type);
		}
		
		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('shares.entity_type', $search);
			$this->db->or_like('users.name', $search);
			$this->db->group_end();
		}
		
		$this->db->order_by('shares.shared_date', 'DESC');
		
		if ($limit) {
			$this->db->limit($limit, $offset);
		}
		
		$query = $this->db->get();
		$results = $query->result();
		
		// Convert binary UUIDs to strings
		foreach ($results as $result) {
			$result->share_id = BIN_TO_UUID($result->share_id);
			$result->entity_id = BIN_TO_UUID($result->entity_id);
			$result->shared_by = BIN_TO_UUID($result->shared_by);
		}
		
		return $results;
	}

	/**
	 * Get count of all shares
	 * 
	 * @param string $search Search term
	 * @param string $entity_type Filter by entity type (optional)
	 * @return int Count of shares
	 */
	public function get_shares_count($search = NULL, $entity_type = NULL)
	{
		$this->db->from($this->db_table('shares'));
		$this->db->join($this->db_table('user_table') . ' AS users', 'users.user_id = shares.shared_by', 'left');
		
		if ($entity_type) {
			$this->db->where('shares.entity_type', $entity_type);
		}
		
		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('shares.entity_type', $search);
			$this->db->or_like('users.name', $search);
			$this->db->group_end();
		}
		
		return $this->db->count_all_results();
	}

	/**
	 * Get sharing statistics
	 * 
	 * @return array Statistics array
	 */
	public function get_sharing_stats()
	{
		// Total shares
		$total_shared = $this->db->count_all_results($this->db_table('shares'));

		// Shares this month
		$this->db->where('shared_date >=', date('Y-m-01 00:00:00'));
		$this->db->where('shared_date <=', date('Y-m-t 23:59:59'));
		$this_month = $this->db->count_all_results($this->db_table('shares'));

		// Shares this week
		$this->db->where('shared_date >=', date('Y-m-d 00:00:00', strtotime('monday this week')));
		$this->db->where('shared_date <=', date('Y-m-d 23:59:59', strtotime('sunday this week')));
		$this_week = $this->db->count_all_results($this->db_table('shares'));

		return array(
			'total_shared' => $total_shared,
			'this_month' => $this_month,
			'this_week' => $this_week
		);
	}

	/**
	 * Check if entity is shared
	 * 
	 * @param string $entity_type The type of entity
	 * @param string $entity_id The UUID of the entity
	 * @return bool TRUE if shared, FALSE otherwise
	 */
	public function is_entity_shared($entity_type, $entity_id)
	{
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		$query = $this->db->get($this->db_table('shares'));
		
		return $query->num_rows() > 0;
	}
}
