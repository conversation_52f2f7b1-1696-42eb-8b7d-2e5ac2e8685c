<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Document_share_model extends Auth_Model
{
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Add a new shared document
	 */
	public function add_share($document_id, $share_url)
	{
		$share_id = UUIDv4();
		$data = array(
			'share_id'    => UUID_TO_BIN($share_id),
			'document_id' => UUID_TO_BIN($document_id),
			'shared_by'   => UUID_TO_BIN($this->auth_user_id),
			'shared_date' => date('Y-m-d H:i:s'),
			'share_url'   => $share_url,
			'is_active'   => 1
		);

		log_message('debug', 'Attempting to insert share data for document: ' . $document_id);
		log_message('debug', 'Table name: ' . $this->db_table('document_shares'));

		if ($this->db->insert($this->db_table('document_shares'), $data)) {
			log_message('debug', 'Share inserted successfully with ID: ' . $share_id);
			return $share_id;
		}

		log_message('error', 'Failed to insert share: ' . $this->db->last_query());
		log_message('error', 'Database error: ' . json_encode($this->db->error()));
		return FALSE;
	}

	/**
	 * Get all shared documents
	 */
	public function get_all_shared_documents($limit = NULL, $offset = NULL, $search = NULL)
	{
		$this->db->select('document_shares.share_id, document_shares.document_id, document_shares.shared_by,
			document_shares.shared_date, document_shares.share_url, document_shares.is_active,
			documents.name as document_name, documents.description as document_description,
			documents.document_type, documents.last_revised, documents.valid_until,
			folders.name as folder_name, menus.name as menu_name, users.name as shared_by_name,
			document_attachment.file_ext');

		$this->db->from($this->db_table('document_shares'));
		$this->db->join($this->db_table('documents'), 'documents.document_id = document_shares.document_id', 'left');
		$this->db->join($this->db_table('folders'), 'folders.folder_id = documents.folder_id', 'left');
		$this->db->join($this->db_table('menus'), 'menus.menu_id = folders.menu_id', 'left');
		$this->db->join($this->db_table('user_table') . ' AS users', 'users.user_id = document_shares.shared_by', 'left');
		$this->db->join($this->db_table('document_attachment'), 'document_attachment.document_id = documents.document_id', 'left');
		
		$this->db->where('document_shares.is_active', 1);
		$this->db->where('documents.status', 'published');

		// Search functionality
		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('documents.name', $search);
			$this->db->or_like('documents.description', $search);
			$this->db->or_like('folders.name', $search);
			$this->db->or_like('menus.name', $search);
			$this->db->group_end();
		}

		// Order by shared_date (which represents the first share date) in descending order
		$this->db->order_by('document_shares.shared_date', 'DESC');

		if ($limit !== NULL) {
			$this->db->limit($limit, $offset);
		}

		$query = $this->db->get();
		
		if ($query->num_rows() > 0) {
			$results = $query->result();
			foreach ($results as $result) {
				$result->share_id = BIN_TO_UUID($result->share_id);
				$result->document_id = BIN_TO_UUID($result->document_id);
				$result->shared_by = BIN_TO_UUID($result->shared_by);
			}
			return $results;
		}
		
		return array();
	}

	/**
	 * Get count of shared documents
	 */
	public function get_shared_documents_count($search = NULL)
	{
		$this->db->from($this->db_table('document_shares'));
		$this->db->join($this->db_table('documents'), 'documents.document_id = document_shares.document_id', 'left');
		$this->db->join($this->db_table('folders'), 'folders.folder_id = documents.folder_id', 'left');
		$this->db->join($this->db_table('menus'), 'menus.menu_id = folders.menu_id', 'left');
		
		$this->db->where('document_shares.is_active', 1);
		$this->db->where('documents.status', 'published');

		// Search functionality
		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('documents.name', $search);
			$this->db->or_like('documents.description', $search);
			$this->db->or_like('folders.name', $search);
			$this->db->or_like('menus.name', $search);
			$this->db->group_end();
		}

		return $this->db->count_all_results();
	}

	/**
	 * Check if document is already shared
	 */
	public function is_document_shared($document_id)
	{
		$this->db->where('document_id', UUID_TO_BIN($document_id));
		$this->db->where('is_active', 1);
		$query = $this->db->get($this->db_table('document_shares'));
		
		return $query->num_rows() > 0;
	}

	/**
	 * Get share information for a document
	 */
	public function get_document_share($document_id)
	{
		$this->db->where('document_id', UUID_TO_BIN($document_id));
		$this->db->where('is_active', 1);
		$query = $this->db->get($this->db_table('document_shares'));

		if ($query->num_rows() > 0) {
			$result = $query->row();
			$result->share_id = BIN_TO_UUID($result->share_id);
			$result->document_id = BIN_TO_UUID($result->document_id);
			$result->shared_by = BIN_TO_UUID($result->shared_by);
			return $result;
		}

		return NULL;
	}

	/**
	 * Update share tracking when document is shared again
	 * Note: We preserve the original shared_date to show when the document was first shared
	 */
	public function update_share_date($document_id)
	{
		$data = array(
			'updated_at' => date('Y-m-d H:i:s')
		);

		$this->db->where('document_id', UUID_TO_BIN($document_id));
		$this->db->where('is_active', 1);

		return $this->db->update($this->db_table('document_shares'), $data);
	}

	/**
	 * Deactivate share
	 */
	public function deactivate_share($document_id)
	{
		$data = array(
			'is_active' => 0,
			'updated_at' => date('Y-m-d H:i:s')
		);
		
		$this->db->where('document_id', UUID_TO_BIN($document_id));
		return $this->db->update($this->db_table('document_shares'), $data);
	}

	/**
	 * Delete share permanently
	 */
	public function delete_share($document_id)
	{
		$this->db->where('document_id', UUID_TO_BIN($document_id));
		return $this->db->delete($this->db_table('document_shares'));
	}

	/**
	 * Remove share by document_id (delete permanently)
	 * This method is used by admin to remove shares
	 */
	public function remove_share($document_id)
	{
		$this->db->where('document_id', UUID_TO_BIN($document_id));
		return $this->db->delete($this->db_table('document_shares'));
	}

	/**
	 * Get sharing statistics
	 */
	public function get_sharing_stats()
	{
		// Total shared documents
		$this->db->where('is_active', 1);
		$total_shared = $this->db->count_all_results($this->db_table('document_shares'));

		// Documents shared this month
		$this->db->where('is_active', 1);
		$this->db->where('shared_date >=', date('Y-m-01 00:00:00'));
		$this->db->where('shared_date <=', date('Y-m-t 23:59:59'));
		$this_month = $this->db->count_all_results($this->db_table('document_shares'));

		// Documents shared this week
		$this->db->where('is_active', 1);
		$this->db->where('shared_date >=', date('Y-m-d 00:00:00', strtotime('monday this week')));
		$this->db->where('shared_date <=', date('Y-m-d 23:59:59', strtotime('sunday this week')));
		$this_week = $this->db->count_all_results($this->db_table('document_shares'));

		return array(
			'total_shared' => $total_shared,
			'this_month' => $this_month,
			'this_week' => $this_week
		);
	}

	/**
	 * Test method to debug SQL query
	 */
	public function test_query()
	{
		$this->db->select('document_shares.share_id, documents.name as document_name, users.name as shared_by_name');
		$this->db->from($this->db_table('document_shares'));
		$this->db->join($this->db_table('documents'), 'documents.document_id = document_shares.document_id', 'left');
		$this->db->join($this->db_table('user_table') . ' AS users', 'users.user_id = document_shares.shared_by', 'left');
		$this->db->limit(1);

		$query = $this->db->get();
		return $query->result();
	}
}
