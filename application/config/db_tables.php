<?php
defined('BASEPATH') OR exit('No direct script access allowed');

// USER RELATED TABLES
$config['user_table']                   = 'users';
$config['user_group']                   = 'user_group';
$config['user_messages']                = 'user_messages';

// LOGIN ERROR RELATED TABLES
$config['errors_table']                 = 'login_errors';
$config['IP_hold_table']                = 'ips_on_hold';
$config['username_or_email_hold_table'] = 'username_or_email_on_hold';
$config['denied_access_table']          = 'denied_access';

// SESSION TABLES
$config['sessions_table']               = 'ci_sessions';
$config['auth_sessions_table']          = 'auth_sessions';

// ACL
$config['acl_users_and_groups_table']   = 'acl_users_and_groups';
$config['acl_categories_table']         = 'acl_categories';
$config['acl_actions_table']            = 'acl_actions';
$config['acl_table']                    = 'acl';

// Groups
$config['groups']                       = 'groups';

// Companies
$config['company']                      = 'companies';

// Memberships
$config['memberships']                  = 'memberships';

// Databases (db)
$config['databases']                    = 'db';

// Menus
$config['menus']                        = 'menus';
$config['menu_group']                   = 'menu_group';

// Folders
$config['folders']                      = 'folders';
$config['folder_group']                 = 'folder_group';

// Tasks
$config['tasks']                        = 'tasks';
$config['task_resource']                = 'task_resource';
$config['task_group']                   = 'task_group';
$config['task_progress']                = 'task_progress';
$config['task_type']                    = 'task_type';
$config['task_metric_type']             = 'task_metric_type';

// Documents
$config['documents']                    = 'documents';
$config['document_readlog']             = 'document_readlog';
$config['document_group']               = 'document_group';
$config['document_category']            = 'document_category';
$config['document_editor']              = 'document_editor';
$config['document_attachment']          = 'document_attachment';
$config['document_education']           = 'document_education';
$config['document_education_done']      = 'document_education_done';
$config['document_education_group']     = 'document_education_group';
$config['document_shares']              = 'document_shares'; // Legacy - will be removed
$config['shares']                       = 'shares';

// Forms
$config['forms']                        = 'forms';
$config['form_pages']                   = 'form_pages';
$config['form_page_questions']          = 'form_page_questions';
$config['form_page_question_options']   = 'form_page_question_options';
$config['form_survey']                  = 'form_survey_';

// Surveys
$config['surveys']                      = 'surveys';
$config['survey_dates']                 = 'survey_dates';
$config['survey_group']                 = 'survey_group';
$config['survey_user']                  = 'survey_user';

// Deviation
$config['deviation']                    = 'deviation';
$config['deviation_answers']            = 'deviation_answers';
$config['deviation_attachment']         = 'deviation_attachment';
$config['deviation_department']         = 'deviation_department';
$config['deviation_fields']             = 'deviation_fields';
$config['deviation_fields_active']      = 'deviation_fields_active';
$config['deviation_email']              = 'deviation_email';
$config['deviation_email_default']      = 'deviation_email_default';
$config['deviation_dropdown']           = 'deviation_dropdown';

// Posts
$config['posts']                        = 'posts';
$config['post_category']                = 'post_category';
$config['post_attachment']              = 'post_attachment';
$config['post_comment']                 = 'post_comment';
$config['post_read']                    = 'post_read';

// Pages
$config['pages']                        = 'pages';
$config['page_category']                = 'page_category';
$config['page_attachment']              = 'page_attachment';

// Categories
$config['categories']                   = 'categories';
$config['category_group']               = 'category_group';

// Eventanalysis
$config['eventanalysis_questions']      = 'eventanalysis_questions';

// Risk
$config['risk_assessments_department']  = 'risk_assessments_department';

// Specialities
$config['specialities']                 = 'specialities';
