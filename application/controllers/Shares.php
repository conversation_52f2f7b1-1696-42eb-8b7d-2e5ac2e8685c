<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Shares extends MY_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->load->model('shares_model');
	}

	/**
	 * Generic endpoint to create/track shares for any entity type
	 * This replaces the Documents::track_share method and can be used by all entities
	 */
	public function track()
	{
		// Allow both AJAX and regular POST requests
		if( $this->input->method(TRUE) !== 'POST' )
		{
			log_message('error', 'Track share: Not a POST request');
			$this->output
					->set_status_header(405)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => 'Method not allowed'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		// Check if user is logged in
		if( ! $this->auth_user_id )
		{
			log_message('error', 'Track share: User not logged in');
			$this->output
					->set_status_header(401)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => lang('document_share_not_authenticated')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		$entity_type = $this->input->post('entity_type');
		$entity_id = $this->input->post('entity_id');

		// Log for debugging
		log_message('debug', 'Track share called with entity_type: ' . $entity_type . ', entity_id: ' . $entity_id . ', user_id: ' . $this->auth_user_id);

		if( empty($entity_type) || empty($entity_id) )
		{
			log_message('error', 'Track share: Missing required parameters');
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => lang('document_share_missing_params')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		// Validate entity_type
		$valid_entity_types = ['document', 'deviation', 'event', 'risk'];
		if( ! in_array($entity_type, $valid_entity_types) )
		{
			log_message('error', 'Track share: Invalid entity_type - ' . $entity_type);
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => 'Invalid entity type'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		// Validate UUID
		try {
			$this->VALID_UUIDv4($entity_id);
		} catch (Exception $e) {
			log_message('error', 'Track share: Invalid UUID - ' . $entity_id);
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => lang('document_share_invalid_id')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		// Verify entity exists and is accessible
		if( ! $this->_verify_entity_access($entity_type, $entity_id) )
		{
			log_message('error', 'Track share: Entity not found or not accessible - ' . $entity_type . ':' . $entity_id);
			$this->output
					->set_status_header(404)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => lang('entity_not_found')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		// Create or get existing share using the generic shares system
		log_message('debug', 'Creating share for ' . $entity_type . ': ' . $entity_id);
		$share_id = $this->shares_model->create_share($entity_type, $entity_id);

		if( $share_id )
		{
			// Build the public URL using share_id
			$public_url = 'https://' . $_SERVER['SERVER_NAME'] . '/share/view/' . $share_id;
			
			log_message('debug', 'Share created/retrieved successfully with ID: ' . $share_id);
			$this->output
					->set_status_header(200)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode([
						'success' => TRUE, 
						'share_id' => $share_id,
						'public_url' => $public_url,
						'entity_type' => $entity_type,
						'entity_id' => $entity_id
					], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}
		else
		{
			log_message('error', 'Failed to create share record');
			$this->output
					->set_status_header(500)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => lang('share_failed')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}
	}

	/**
	 * Verify that an entity exists and is accessible for sharing
	 * 
	 * @param string $entity_type The type of entity
	 * @param string $entity_id The UUID of the entity
	 * @return bool TRUE if entity exists and is accessible, FALSE otherwise
	 */
	private function _verify_entity_access($entity_type, $entity_id)
	{
		switch ($entity_type) {
			case 'document':
				$this->load->model('document_model');
				$entity = $this->document_model->get($entity_id);
				return !empty($entity) && $entity->status === 'published';
				
			case 'deviation':
				$this->load->model('deviation_model');
				$entity = $this->deviation_model->deviationRegistredBy($entity_id);
				return !empty($entity);
				
			case 'event':
				$this->load->library('eventanalysislib');
				$entity = $this->eventanalysislib->getEventAnalysis($entity_id);
				return !empty($entity);
				
			case 'risk':
				$this->load->library('riskassessmentslib');
				$entity = $this->riskassessmentslib->getRisk($entity_id);
				return !empty($entity);
				
			default:
				return FALSE;
		}
	}
}
