<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Share extends MY_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->load->model(['shares_model', 'document_model', 'deviation_model', 'group_model', 'user_model']);
		$this->load->library(['eventanalysislib', 'riskassessmentslib']);
		$this->users = $this->user_model->get_all();
	}

	/**
	 * View a shared entity by share_id
	 * This is the public endpoint that doesn't require authentication
	 * 
	 * @param string $share_id The UUID of the share
	 */
	public function view($share_id = NULL)
	{
		if (empty($share_id)) {
			show_404();
		}

		$this->VALID_UUIDv4($share_id);

		// Get the share record
		$share = $this->shares_model->get_by_share_id($share_id);
		if (empty($share)) {
			log_message('debug', 'Share not found for share_id: ' . $share_id);
			show_404();
		}

		log_message('debug', 'Found share for entity_type: ' . $share->entity_type . ', entity_id: ' . $share->entity_id);

		// Route to the appropriate entity handler based on entity_type
		switch ($share->entity_type) {
			case 'document':
				$this->_view_document($share->entity_id);
				break;
			case 'deviation':
				$this->_view_deviation($share->entity_id);
				break;
			case 'event':
				$this->_view_event_analysis($share->entity_id);
				break;
			case 'risk':
				$this->_view_risk_assessment($share->entity_id);
				break;
			default:
				log_message('error', 'Unknown entity_type in share: ' . $share->entity_type);
				show_404();
		}
	}

	/**
	 * View a shared document
	 * 
	 * @param string $document_id The UUID of the document
	 */
	private function _view_document($document_id)
	{
		$this->data['document'] = $this->document_model->get($document_id);
		if (empty($this->data['document'])) {
			log_message('debug', 'Document not found: ' . $document_id);
			show_404();
		}

		// Check if document is published
		if ($this->data['document']->status !== 'published') {
			log_message('debug', 'Document not published: ' . $document_id);
			show_404();
		}

		$this->load->helper('form');
		$this->load->model(['folder_model', 'menu_model']);
		
		$this->data['folder'] = $this->folder_model->get($this->data['document']->folder_id);
		$this->data['menu'] = $this->menu_model->get($this->data['folder']->menu_id);

		$this->data['groups'] = $this->group_model->get_all_by_relationship('folder_group', 'folder_id', $this->data['document']->folder_id, FALSE);
		if (empty($this->data['groups'])) {
			$this->data['groups'] = $this->group_model->get_all_by_relationship('menu_group', 'menu_id', $this->data['menu']->menu_id, FALSE);
		}

		$this->load->view('general/documents/viewpublic', $this->data);
	}

	/**
	 * View a shared deviation
	 * 
	 * @param string $deviation_id The UUID of the deviation
	 */
	private function _view_deviation($deviation_id)
	{
		$this->data['deviation']['registred'] = $this->deviation_model->deviationRegistredBy($deviation_id);
		if (empty($this->data['deviation']['registred'])) {
			log_message('debug', 'Deviation not found: ' . $deviation_id);
			show_404();
		}

		$this->data['deviation']['fields'] = $this->deviation_model->getDeviationPageAndOrId($deviation_id);
		if (empty($this->data['deviation']['fields'])) {
			log_message('debug', 'Deviation fields not found: ' . $deviation_id);
			show_404();
		}

		$this->data['a_id'] = $deviation_id;
		$this->data['attachments'] = $this->deviation_model->get_attachments($deviation_id);
		$this->data['fields'] = $this->deviation_model->getDeviationFieldsByPage(array(1, 2, 3));
		$this->data['options'] = $this->deviation_model->getDropdown();
		$this->data['groups'] = $this->group_model->get_all();
		$this->data['selected'] = [];

		$departments = $this->deviation_model->deviationDepartment($deviation_id);

		$this->_setOptions($this->data['fields'], $this->data['options'], $departments);
		$this->_setSelected($this->data['fields'], $this->data['selected'], $departments, $deviation_id);
		
		$this->load->view('general/deviation/viewpublic', $this->data);
	}

	/**
	 * View a shared event analysis
	 * 
	 * @param string $event_id The UUID of the event analysis
	 */
	private function _view_event_analysis($event_id)
	{
		$getEventAnalysis = $this->eventanalysislib->getEventAnalysis($event_id);
		if (empty($getEventAnalysis)) {
			log_message('debug', 'Event analysis not found: ' . $event_id);
			show_404();
		}

		list($getEventAnalysisQuestions, $getEventAnalysisQuestionsAnswers) = $this->eventanalysislib->getEventAnalysisQuestions($event_id);
		$getEventAnalysisActionList = $this->eventanalysislib->getEventAnalysisActionList($event_id);

		$this->data['getEventAnalysis'] = $getEventAnalysis;
		$this->data['getEventAnalysisQuestions'] = $getEventAnalysisQuestions;
		$this->data['getEventAnalysisQuestionsAnswers'] = $getEventAnalysisQuestionsAnswers;
		$this->data['getEventAnalysisActionList'] = $getEventAnalysisActionList;

		// Set up action list fields (copied from Eventanalysisview controller)
		$this->load->helper(array('form', 'forms'));
		$this->load->helper('old_form');
		
		$this->data['actionListFields'] = array(
			'responsible' => array(
				'type' => 'select',
				'name' => 'eventActionListResponsible[]',
				'title' => 'Ansvarig',
				'values' => $this->users
			),
			'deadline' => array(
				'type' => 'date',
				'name' => 'eventActionListDeadline[]',
				'title' => 'Deadline'
			)
		);

		$this->data['actionListFields'] = array_merge($this->data['actionListFields'], array(
			'description' => array(
				'type' => 'textarea',
				'name' => 'eventActionListDescription[]',
				'title' => 'Beskrivning',
				'placeholder' => 'Förklarande text...'
			),
			'done' => array(
				'title' => 'Är åtgärden färdig?',
				'type' => 'checkbox',
				'name' => 'eventActionListDone[]',
				'values' => 1,
				'title' => 'Är åtgärden färdig?',
			)
		));

		$this->load->view('general/eventanalysis/viewpublic', $this->data);
	}

	/**
	 * View a shared risk assessment
	 * 
	 * @param string $risk_id The UUID of the risk assessment
	 */
	private function _view_risk_assessment($risk_id)
	{
		$getRisk = $this->riskassessmentslib->getRisk($risk_id);
		if (empty($getRisk)) {
			log_message('debug', 'Risk assessment not found: ' . $risk_id);
			show_404();
		}

		$getRiskDepartment = $this->riskassessmentslib->getRiskDepartment($risk_id);
		$getRisks = $this->riskassessmentslib->getRisks($risk_id);

		$this->data['getRisk'] = $getRisk;
		$this->data['getRiskDepartment'] = $getRiskDepartment;
		$this->data['getRisks'] = $getRisks;

		$this->load->view('general/riskassessments/viewpublic', $this->data);
	}

	/**
	 * Set options for deviation fields (copied from Deviationview controller)
	 */
	private function _setOptions($fields, &$options, $departments)
	{
		if( ! empty($fields['type']['department']) )
		{
			foreach($fields['type']['department'] as $department)
			{
				foreach($departments as $d)
				{
					if( isset($this->data['groups']['department'][$d]) ) {
						$dropdown = new stdClass();
						$dropdown->option_id = $d;
						$dropdown->name      = $this->data['groups']['department'][$d];
						$options[$department][$d] = $dropdown;
					}
				}
			}
		}
		if( ! empty($fields['type']['email']) )
		{
			foreach($fields['type']['email'] as $email)
			{
				foreach($this->users as $user)
				{
					$dropdown = new stdClass();
					$dropdown->option_id = $user->user_id;
					$dropdown->name      = $user->name;
					$options[$email][$user->user_id] = $dropdown;
				}
			}
		}

		if( ! empty($fields['type']['users']) )
		{
			foreach($fields['type']['users'] as $users)
			{
				foreach($this->users as $user)
				{
					$dropdown = new stdClass();
					$dropdown->option_id = $user->user_id;
					$dropdown->name      = $user->name;
					$options[$users][$user->user_id] = $dropdown;
				}
			}
		}
		if( ! empty($fields['type']['eventanalysis']) )
		{
			foreach($fields['type']['eventanalysis'] as $eventanalysis)
			{
				$dropdown = new stdClass();
				$dropdown->option_id = 0;
				$dropdown->name      = 'Ingen djupare händelesanalys krävs';
				$options[$eventanalysis][0] = $dropdown;
				foreach($this->users as $user)
				{
					$dropdown = new stdClass();
					$dropdown->option_id = $eventanalysis . '_' . $user->user_id;
					$dropdown->name      = $user->name;
					$options[$eventanalysis][$user->user_id] = $dropdown;
				}
			}
		}
	}

	/**
	 * Set selected values for deviation fields (copied from Deviationview controller)
	 */
	private function _setSelected($fields, &$selected, $departments, $id)
	{
		if( ! empty($fields['type']['department']) )
		{
			foreach($fields['type']['department'] as $department)
			{
				$selected[$department] = $departments;
			}
		}

		$emails = $this->deviation_model->get_email($id);

		if( ! empty($this->data['fields']['type']['email']) && ! empty($emails) )
		{
			foreach($this->data['fields']['type']['email'] as $email)
			{
				if( isset($emails[$email]) )
				{
					foreach($emails[$email] as $user_id)
					{
						if( ! isset($this->users[$user_id]) )
							continue;

						$selected[$email][] = $this->users[$user_id]->name;
					}
				}
			}
		}
	}
}
