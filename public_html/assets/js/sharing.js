/**
 * Generic sharing functionality for all entity types
 * This replaces the individual trackDocumentShare, trackDeviationShare, etc. functions
 */

/**
 * Track sharing for any entity type
 * @param {string} entityType - The type of entity (document, deviation, event, risk)
 * @param {string} entityId - The UUID of the entity
 * @param {string} csrfTokenName - The CSRF token name
 * @param {string} csrfTokenValue - The CSRF token value
 */
function trackEntityShare(entityType, entityId, csrfTokenName, csrfTokenValue) {
	var data = {
		entity_type: entityType,
		entity_id: entityId
	};
	data[csrfTokenName] = csrfTokenValue;

	$.ajax({
		url: '/shares/track',
		type: 'POST',
		data: data,
		dataType: 'json',
		success: function(response) {
			console.log('Share tracked successfully:', response);
			if (response.success && response.public_url) {
				// Copy the public URL to clipboard
				copyToClipboard(response.public_url);
				showNotification(getLanguageString('shareable_link_copied'), 'success');
			}
		},
		error: function(xhr, status, error) {
			console.log('Failed to track share:', error);
			console.log('Response:', xhr.responseText);
			showNotification(getLanguageString('share_failed'), 'error');
		}
	});
}

/**
 * Legacy function for document sharing - now uses the generic system
 * @param {string} documentId - The UUID of the document
 */
function trackDocumentShare(documentId) {
	// Get CSRF token from the page (assuming it's available globally)
	var csrfTokenName = window.csrfTokenName || 'csrf_token';
	var csrfTokenValue = window.csrfTokenValue || '';
	
	trackEntityShare('document', documentId, csrfTokenName, csrfTokenValue);
}

/**
 * Track deviation sharing
 * @param {string} deviationId - The UUID of the deviation
 */
function trackDeviationShare(deviationId) {
	var csrfTokenName = window.csrfTokenName || 'csrf_token';
	var csrfTokenValue = window.csrfTokenValue || '';
	
	trackEntityShare('deviation', deviationId, csrfTokenName, csrfTokenValue);
}

/**
 * Track event analysis sharing
 * @param {string} eventId - The UUID of the event analysis
 */
function trackEventShare(eventId) {
	var csrfTokenName = window.csrfTokenName || 'csrf_token';
	var csrfTokenValue = window.csrfTokenValue || '';
	
	trackEntityShare('event', eventId, csrfTokenName, csrfTokenValue);
}

/**
 * Track risk assessment sharing
 * @param {string} riskId - The UUID of the risk assessment
 */
function trackRiskShare(riskId) {
	var csrfTokenName = window.csrfTokenName || 'csrf_token';
	var csrfTokenValue = window.csrfTokenValue || '';
	
	trackEntityShare('risk', riskId, csrfTokenName, csrfTokenValue);
}

/**
 * Copy text to clipboard with fallback
 * @param {string} text - The text to copy
 */
function copyToClipboard(text) {
	if (navigator.clipboard) {
		navigator.clipboard.writeText(text).then(function() {
			// Success handled in the calling function
		}).catch(function() {
			fallbackCopyTextToClipboard(text);
		});
	} else {
		fallbackCopyTextToClipboard(text);
	}
}

/**
 * Fallback method for copying text to clipboard
 * @param {string} text - The text to copy
 */
function fallbackCopyTextToClipboard(text) {
	var textArea = document.createElement("textarea");
	textArea.value = text;
	document.body.appendChild(textArea);
	textArea.focus();
	textArea.select();
	try {
		var successful = document.execCommand('copy');
		if (!successful) {
			showNotification(getLanguageString('shareable_copy_failed'), 'error');
		}
	} catch (err) {
		showNotification(getLanguageString('shareable_copy_failed'), 'error');
	}
	document.body.removeChild(textArea);
}

/**
 * Get language string with fallback
 * @param {string} key - The language key
 * @returns {string} The translated string or the key if not found
 */
function getLanguageString(key) {
	// This assumes language strings are available globally
	if (typeof window.lang !== 'undefined' && window.lang[key]) {
		return window.lang[key];
	}
	
	// Fallback to English strings
	var fallbacks = {
		'shareable_link_copied': 'Share link copied to clipboard',
		'shareable_copy_failed': 'Failed to copy link',
		'share_failed': 'Failed to create share'
	};
	
	return fallbacks[key] || key;
}

/**
 * Show notification (assumes this function exists globally)
 * @param {string} message - The message to show
 * @param {string} type - The type of notification (success, error, info, warning)
 */
function showNotification(message, type) {
	// This function should be implemented by the main application
	// For now, we'll use console.log as fallback
	if (typeof window.showNotification === 'function') {
		window.showNotification(message, type);
	} else {
		console.log(type.toUpperCase() + ': ' + message);
		// Simple alert as last resort
		if (type === 'error') {
			alert('Error: ' + message);
		}
	}
}
