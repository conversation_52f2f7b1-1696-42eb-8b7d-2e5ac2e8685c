<?php
/**
 * Test script to verify the unified shares migration
 * This script should be run once to migrate from document_shares to shares
 * DELETE THIS FILE AFTER TESTING
 */

// Include CodeIgniter bootstrap
define('BASEPATH', TRUE);
require_once 'application/config/database.php';

echo "<h1>Unified Shares Migration Test</h1>";

try {
    // Connect to database using CI config
    $pdo = new PDO(
        "mysql:host=" . CI_DB_HOSTNAME . ";dbname=" . CI_DB_DATABASE,
        CI_DB_USERNAME,
        CI_DB_PASSWORD,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );

    echo "<h2>1. Check existing document_shares table</h2>";
    
    // Check if document_shares table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'document_shares'");
    if ($stmt->rowCount() > 0) {
        echo "✅ document_shares table exists<br>";
        
        // Count active shares
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM document_shares WHERE is_active = 1");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "📊 Active document shares: " . $result['count'] . "<br>";
    } else {
        echo "❌ document_shares table does not exist<br>";
    }

    echo "<h2>2. Check if shares table exists</h2>";
    
    // Check if shares table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'shares'");
    if ($stmt->rowCount() > 0) {
        echo "✅ shares table already exists<br>";
        
        // Count shares
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM shares");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "📊 Total shares: " . $result['count'] . "<br>";
    } else {
        echo "❌ shares table does not exist - migration needed<br>";
        
        echo "<h2>3. Running migration...</h2>";
        
        // Read and execute the migration SQL
        $migrationSql = file_get_contents('sql/update_unified_shares.sql');
        
        // Split by semicolon and execute each statement
        $statements = array_filter(array_map('trim', explode(';', $migrationSql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                try {
                    $pdo->exec($statement);
                    echo "✅ Executed: " . substr($statement, 0, 50) . "...<br>";
                } catch (PDOException $e) {
                    echo "❌ Error executing: " . substr($statement, 0, 50) . "... - " . $e->getMessage() . "<br>";
                }
            }
        }
        
        echo "<h2>4. Verify migration results</h2>";
        
        // Check shares table again
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM shares");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "📊 Total shares after migration: " . $result['count'] . "<br>";
        
        // Show sample data
        $stmt = $pdo->query("SELECT BIN_TO_UUID(share_id) as share_id, entity_type, BIN_TO_UUID(entity_id) as entity_id, shared_date FROM shares LIMIT 5");
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($results)) {
            echo "<h3>Sample migrated data:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Share ID</th><th>Entity Type</th><th>Entity ID</th><th>Shared Date</th></tr>";
            foreach ($results as $row) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['share_id']) . "</td>";
                echo "<td>" . htmlspecialchars($row['entity_type']) . "</td>";
                echo "<td>" . htmlspecialchars($row['entity_id']) . "</td>";
                echo "<td>" . htmlspecialchars($row['shared_date']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }

    echo "<h2>5. Test share creation</h2>";
    
    // Test creating a new share (using dummy data)
    $testEntityId = '12345678-1234-1234-1234-123456789012'; // Dummy UUID
    $testUserId = '*************-4321-4321-************';   // Dummy UUID
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO shares (share_id, entity_type, entity_id, shared_by, shared_date) 
            VALUES (UUID_TO_BIN(UUID()), 'document', UUID_TO_BIN(?), UUID_TO_BIN(?), NOW())
            ON DUPLICATE KEY UPDATE shared_date = shared_date
        ");
        $stmt->execute([$testEntityId, $testUserId]);
        echo "✅ Test share creation successful<br>";
    } catch (PDOException $e) {
        echo "❌ Test share creation failed: " . $e->getMessage() . "<br>";
    }

    echo "<h2>✅ Migration test completed successfully!</h2>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Test the sharing functionality in the application</li>";
    echo "<li>Verify that public share URLs work correctly</li>";
    echo "<li>Once confirmed working, you can drop the document_shares table</li>";
    echo "<li>Delete this test file</li>";
    echo "</ul>";

} catch (PDOException $e) {
    echo "<h2>❌ Database connection failed</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration.</p>";
}
?>
