-- Create table to track shared documents
CREATE TABLE IF NOT EXISTS `document_shares` (
  `share_id` binary(16) NOT NULL,
  `document_id` binary(16) NOT NULL,
  `shared_by` binary(16) NOT NULL,
  `shared_date` datetime NOT NULL,
  `share_url` varchar(500) COLLATE utf8mb4_swedish_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`share_id`),
  <PERSON>EY `idx_document_id` (`document_id`),
  KEY `idx_shared_by` (`shared_by`),
  KEY `idx_shared_date` (`shared_date`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_document_shares_document` FOREI<PERSON><PERSON> KEY (`document_id`) REFERENCES `documents` (`document_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_document_shares_user` FOREIGN KEY (`shared_by`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_swedish_ci;

-- Add table to db_tables.php configuration
