-- Database update to add shareable documents feature
-- Create table to track shared documents
CREATE TABLE IF NOT EXISTS `document_shares` (
  `share_id` binary(16) NOT NULL,
  `document_id` binary(16) NOT NULL,
  `shared_by` binary(16) NOT NULL,
  `shared_date` datetime NOT NULL,
  `share_url` varchar(500) COLLATE utf8mb4_swedish_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`share_id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_shared_by` (`shared_by`),
  KEY `idx_shared_date` (`shared_date`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `fk_document_shares_document` FOREIGN KEY (`document_id`) REFERENCES `documents` (`document_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_document_shares_user` FOREIGN KEY (`shared_by`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_swedish_ci;

-- Add additional indexes for performance improvement
CREATE INDEX `idx_document_shares_active_date` ON `document_shares` (`is_active`, `shared_date`);
CREATE INDEX `idx_document_shares_document_active` ON `document_shares` (`document_id`, `is_active`);

-- Update database settings
SET foreign_key_checks = 1;
