-- Database migration to create unified shares system
-- This replaces document_shares with a generic shares table for all entity types

-- Create the new generic shares table
CREATE TABLE IF NOT EXISTS `shares` (
  `share_id` binary(16) NOT NULL,
  `entity_type` varchar(50) COLLATE utf8mb4_swedish_ci NOT NULL,
  `entity_id` binary(16) NOT NULL,
  `shared_by` binary(16) NOT NULL,
  `shared_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`share_id`),
  UNIQUE KEY `idx_entity_unique` (`entity_type`, `entity_id`),
  KEY `idx_shared_by` (`shared_by`),
  KEY `idx_shared_date` (`shared_date`),
  KEY `idx_entity_lookup` (`entity_type`, `entity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_swedish_ci;

-- Migrate existing document_shares data to the new shares table
-- Only migrate active shares and preserve the original shared_date
INSERT INTO `shares` (`share_id`, `entity_type`, `entity_id`, `shared_by`, `shared_date`)
SELECT 
    `share_id`,
    'document' as `entity_type`,
    `document_id` as `entity_id`,
    `shared_by`,
    `shared_date`
FROM `document_shares`
WHERE `is_active` = 1
ON DUPLICATE KEY UPDATE
    `shared_date` = VALUES(`shared_date`);

-- Drop the old document_shares table after successful migration
-- Note: This will be done after verifying the migration was successful
-- DROP TABLE IF EXISTS `document_shares`;

-- Add foreign key constraints (optional, depending on your FK policy)
-- ALTER TABLE `shares` ADD CONSTRAINT `fk_shares_user` FOREIGN KEY (`shared_by`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;

-- Update database settings
SET foreign_key_checks = 1;
